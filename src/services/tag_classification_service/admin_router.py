"""
标签分类服务B端管理API路由（权限修复版）
定义标签分类相关的管理HTTP接口，使用新的TagClassification模型
"""

from math import ceil
from typing import Annotated, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy import func
from sqlalchemy.orm import Session

from ...core.database import get_db
from ..permission_service.dependencies import require_admin, require_permission
from ..user_service.dependencies import get_current_active_user
from ..user_service.models import User
from .models import Tag, TagClassification
from .dependencies import (
    get_classification_service,
    get_tag_classification_service,
    get_tag_service,
)
from .schemas import (
    # 标签分类相关
    TagClassificationCreate,
    TagClassificationResponse,
    TagClassificationUpdate,
    # 标签相关
    TagCreate,
    TagResponse,
    TagUpdate,
    # 分类相关
    ClassificationDimensionCreate,
    ClassificationDimensionResponse,
    ClassificationDimensionUpdate,
    ClassificationValueCreate,
    ClassificationValueResponse,
    ClassificationValueUpdate,
    # 通用响应
    PageResponse,
)
from .service import (
    ClassificationService,
    TagClassificationService,
    TagService,
)

# 创建路由器
router = APIRouter()


def get_client_info(request: Request) -> tuple[str, str]:
    """
    获取客户端信息

    Args:
        request: FastAPI请求对象

    Returns:
        IP地址和用户代理的元组
    """
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent", "")
    return ip_address, user_agent


# ==================== 标签分类管理接口 ====================

@router.get(
    "/tags/classifications",
    response_model=PageResponse,
    summary="获取标签分类列表",
    description="获取标签分类分页列表 (需要标签分类读取权限)",
)
async def get_tag_classifications(
    current_user: Annotated[User, Depends(require_permission("tag.classification.read"))],
    tag_classification_service: Annotated[TagClassificationService, Depends(get_tag_classification_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    classification_type: Optional[str] = Query(default=None, description="分类类型过滤"),
    domain: Optional[str] = Query(default=None, description="业务域过滤"),
    parent_id: Optional[int] = Query(default=None, description="父分类ID过滤"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃分类"),
):
    """获取标签分类列表"""
    try:
        skip = (page - 1) * size
        classifications = tag_classification_service.get_classifications(
            classification_type=classification_type,
            domain=domain,
            parent_id=parent_id,
            is_active=is_active,
            skip=skip,
            limit=size
        )

        # 简化处理，实际应该返回总数
        total = len(classifications)
        
        return PageResponse(
            items=[TagClassificationResponse.model_validate(c) for c in classifications],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size) if total > 0 else 1,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag classifications: {str(e)}",
        )


@router.get(
    "/tags/classifications/{classification_id}",
    response_model=TagClassificationResponse,
    summary="获取标签分类详情",
    description="根据ID获取标签分类详情 (需要标签分类读取权限)",
)
async def get_tag_classification(
    classification_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.classification.read"))],
    tag_classification_service: Annotated[TagClassificationService, Depends(get_tag_classification_service)],
):
    """获取标签分类详情"""
    classification = tag_classification_service.get_classification_by_id(classification_id)

    if not classification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="Tag classification not found"
        )

    return TagClassificationResponse.model_validate(classification)


@router.post(
    "/tags/classifications",
    response_model=TagClassificationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建标签分类",
    description="创建新的标签分类 (需要标签分类创建权限)",
)
async def create_tag_classification(
    classification_data: TagClassificationCreate,
    current_user: Annotated[User, Depends(require_permission("tag.classification.create"))],
    tag_classification_service: Annotated[TagClassificationService, Depends(get_tag_classification_service)],
):
    """创建标签分类"""
    try:
        classification = tag_classification_service.create_classification(classification_data)
        return TagClassificationResponse.model_validate(classification)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create tag classification: {str(e)}",
        )


@router.put(
    "/tags/classifications/{classification_id}",
    response_model=TagClassificationResponse,
    summary="更新标签分类",
    description="更新标签分类信息 (需要标签分类更新权限)",
)
async def update_tag_classification(
    classification_id: int,
    classification_data: TagClassificationUpdate,
    current_user: Annotated[User, Depends(require_permission("tag.classification.update"))],
    tag_classification_service: Annotated[TagClassificationService, Depends(get_tag_classification_service)],
):
    """更新标签分类"""
    try:
        classification = tag_classification_service.update_classification(classification_id, classification_data)
        
        if not classification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tag classification not found"
            )
        
        return TagClassificationResponse.model_validate(classification)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update tag classification: {str(e)}",
        )


@router.delete(
    "/tags/classifications/{classification_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除标签分类",
    description="删除标签分类 (需要标签分类删除权限)",
)
async def delete_tag_classification(
    classification_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.classification.delete"))],
    tag_classification_service: Annotated[TagClassificationService, Depends(get_tag_classification_service)],
):
    """删除标签分类"""
    try:
        success = tag_classification_service.delete_classification(classification_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tag classification not found"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete tag classification: {str(e)}",
        )


@router.get(
    "/tags/classifications/tree",
    response_model=List[TagClassificationResponse],
    summary="获取标签分类树",
    description="获取标签分类的树形结构 (需要标签分类读取权限)",
)
async def get_tag_classifications_tree(
    current_user: Annotated[User, Depends(require_permission("tag.classification.read"))],
    tag_classification_service: Annotated[TagClassificationService, Depends(get_tag_classification_service)],
    domain: Optional[str] = Query(default=None, description="业务域过滤"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃分类"),
):
    """获取标签分类树形结构"""
    try:
        # 获取根节点分类
        root_classifications = tag_classification_service.get_classifications(
            domain=domain,
            parent_id=None,  # 只获取根节点
            is_active=is_active,
            skip=0,
            limit=1000  # 假设根节点不会太多
        )
        
        return [TagClassificationResponse.model_validate(c) for c in root_classifications]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag classifications tree: {str(e)}",
        )


# ==================== 标签管理接口 ====================

@router.get(
    "/tags",
    response_model=PageResponse,
    summary="获取标签列表",
    description="获取标签分页列表 (需要标签读取权限)",
)
async def get_tags(
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(default=None, description="搜索关键词"),
    classification_id: Optional[int] = Query(default=None, description="标签分类ID"),
    parent_id: Optional[int] = Query(default=None, description="父标签ID"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃标签"),
):
    """获取标签列表"""
    try:
        from .schemas import TagListQuery
        
        query = TagListQuery(
            page=page,
            size=size,
            search=search,
            classification_id=classification_id,
            parent_id=parent_id,
            is_active=is_active
        )
        
        tags, total = tag_service.list_tags(query)
        
        return PageResponse(
            items=[TagResponse.model_validate(tag) for tag in tags],
            total=total,
            page=page,
            size=size,
            pages=ceil(total / size) if total > 0 else 1,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tags: {str(e)}",
        )


@router.get(
    "/tags/{tag_id}",
    response_model=TagResponse,
    summary="获取标签详情",
    description="根据ID获取标签详情 (需要标签读取权限)",
)
async def get_tag(
    tag_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
):
    """获取标签详情"""
    tag = tag_service.get_tag_by_id(tag_id, include_relations=True)

    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found"
        )

    return TagResponse.model_validate(tag)


@router.post(
    "/tags",
    response_model=TagResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建标签",
    description="创建新的标签 (需要标签创建权限)",
)
async def create_tag(
    tag_data: TagCreate,
    current_user: Annotated[User, Depends(require_permission("tag.tag.create"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
):
    """创建标签"""
    try:
        tag = tag_service.create_tag(tag_data)
        return TagResponse.model_validate(tag)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create tag: {str(e)}",
        )


@router.put(
    "/tags/{tag_id}",
    response_model=TagResponse,
    summary="更新标签",
    description="更新标签信息 (需要标签更新权限)",
)
async def update_tag(
    tag_id: int,
    tag_data: TagUpdate,
    current_user: Annotated[User, Depends(require_permission("tag.tag.update"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
):
    """更新标签"""
    try:
        tag = tag_service.update_tag(tag_id, tag_data)

        if not tag:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tag not found"
            )

        return TagResponse.model_validate(tag)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update tag: {str(e)}",
        )


@router.delete(
    "/tags/{tag_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除标签",
    description="删除标签 (需要标签删除权限)",
)
async def delete_tag(
    tag_id: int,
    current_user: Annotated[User, Depends(require_permission("tag.tag.delete"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
):
    """删除标签"""
    try:
        success = tag_service.delete_tag(tag_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tag not found"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete tag: {str(e)}",
        )


# ==================== 分类维度管理接口 ====================

@router.get(
    "/classifications/dimensions",
    response_model=List[ClassificationDimensionResponse],
    summary="获取分类维度列表",
    description="获取所有分类维度 (需要分类维度读取权限)",
)
async def get_classification_dimensions_admin(
    current_user: Annotated[User, Depends(require_permission("classification.dimension.read"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃维度"),
):
    """获取分类维度列表"""
    try:
        dimensions, _ = classification_service.get_classification_dimensions(
            skip=0, limit=1000, is_active=is_active
        )
        return [
            ClassificationDimensionResponse.model_validate(dim) for dim in dimensions
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification dimensions: {str(e)}",
        )


@router.get(
    "/classifications/dimensions/{dimension_id}",
    response_model=ClassificationDimensionResponse,
    summary="获取分类维度详情",
    description="根据ID获取分类维度详情 (需要分类维度读取权限)",
)
async def get_classification_dimension_admin(
    dimension_id: int,
    current_user: Annotated[User, Depends(require_permission("classification.dimension.read"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """获取分类维度详情"""
    dimension = classification_service.get_classification_dimension_by_id(dimension_id)

    if not dimension:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Classification dimension not found"
        )

    return ClassificationDimensionResponse.model_validate(dimension)


@router.post(
    "/classifications/dimensions",
    response_model=ClassificationDimensionResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建分类维度",
    description="创建新的分类维度 (需要分类维度创建权限)",
)
async def create_classification_dimension_admin(
    dimension_data: ClassificationDimensionCreate,
    current_user: Annotated[User, Depends(require_permission("classification.dimension.create"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """创建分类维度"""
    try:
        dimension = classification_service.create_classification_dimension(dimension_data)
        return ClassificationDimensionResponse.model_validate(dimension)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create classification dimension: {str(e)}",
        )


@router.put(
    "/classifications/dimensions/{dimension_id}",
    response_model=ClassificationDimensionResponse,
    summary="更新分类维度",
    description="更新分类维度信息 (需要分类维度更新权限)",
)
async def update_classification_dimension_admin(
    dimension_id: int,
    dimension_data: ClassificationDimensionUpdate,
    current_user: Annotated[User, Depends(require_permission("classification.dimension.update"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """更新分类维度"""
    try:
        dimension = classification_service.update_classification_dimension(dimension_id, dimension_data)

        if not dimension:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification dimension not found"
            )

        return ClassificationDimensionResponse.model_validate(dimension)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update classification dimension: {str(e)}",
        )


@router.delete(
    "/classifications/dimensions/{dimension_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除分类维度",
    description="删除分类维度 (需要分类维度删除权限)",
)
async def delete_classification_dimension_admin(
    dimension_id: int,
    current_user: Annotated[User, Depends(require_permission("classification.dimension.delete"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """删除分类维度"""
    try:
        success = classification_service.delete_classification_dimension(dimension_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification dimension not found"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete classification dimension: {str(e)}",
        )


# ==================== 分类值管理接口 ====================

@router.get(
    "/classifications/values",
    response_model=List[ClassificationValueResponse],
    summary="获取分类值列表",
    description="获取分类值列表 (需要分类值读取权限)",
)
async def get_classification_values_admin(
    current_user: Annotated[User, Depends(require_permission("classification.value.read"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
    dimension_id: Optional[int] = Query(default=None, description="分类维度ID过滤"),
    is_active: Optional[bool] = Query(default=None, description="是否只获取活跃值"),
):
    """获取分类值列表"""
    try:
        values, _ = classification_service.get_classification_values(
            dimension_id=dimension_id,
            skip=0,
            limit=1000,
            is_active=is_active
        )
        return [
            ClassificationValueResponse.model_validate(val) for val in values
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get classification values: {str(e)}",
        )


@router.get(
    "/classifications/values/{value_id}",
    response_model=ClassificationValueResponse,
    summary="获取分类值详情",
    description="根据ID获取分类值详情 (需要分类值读取权限)",
)
async def get_classification_value_admin(
    value_id: int,
    current_user: Annotated[User, Depends(require_permission("classification.value.read"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """获取分类值详情"""
    value = classification_service.get_classification_value_by_id(value_id)

    if not value:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Classification value not found"
        )

    return ClassificationValueResponse.model_validate(value)


@router.post(
    "/classifications/values",
    response_model=ClassificationValueResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建分类值",
    description="创建新的分类值 (需要分类值创建权限)",
)
async def create_classification_value_admin(
    value_data: ClassificationValueCreate,
    current_user: Annotated[User, Depends(require_permission("classification.value.create"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """创建分类值"""
    try:
        value = classification_service.create_classification_value(value_data)
        return ClassificationValueResponse.model_validate(value)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create classification value: {str(e)}",
        )


@router.put(
    "/classifications/values/{value_id}",
    response_model=ClassificationValueResponse,
    summary="更新分类值",
    description="更新分类值信息 (需要分类值更新权限)",
)
async def update_classification_value_admin(
    value_id: int,
    value_data: ClassificationValueUpdate,
    current_user: Annotated[User, Depends(require_permission("classification.value.update"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """更新分类值"""
    try:
        value = classification_service.update_classification_value(value_id, value_data)

        if not value:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification value not found"
            )

        return ClassificationValueResponse.model_validate(value)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update classification value: {str(e)}",
        )


@router.delete(
    "/classifications/values/{value_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除分类值",
    description="删除分类值 (需要分类值删除权限)",
)
async def delete_classification_value_admin(
    value_id: int,
    current_user: Annotated[User, Depends(require_permission("classification.value.delete"))],
    classification_service: Annotated[ClassificationService, Depends(get_classification_service)],
):
    """删除分类值"""
    try:
        success = classification_service.delete_classification_value(value_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Classification value not found"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete classification value: {str(e)}",
        )


# ==================== 统计和分析接口 ====================

@router.get(
    "/tags/statistics",
    summary="获取标签统计信息",
    description="获取标签相关的统计信息 (需要标签读取权限)",
)
async def get_tag_statistics(
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    tag_classification_service: Annotated[TagClassificationService, Depends(get_tag_classification_service)],
    db: Annotated[Session, Depends(get_db)],
):
    """获取标签统计信息"""
    try:
        # 统计标签分类数量
        total_classifications = db.query(func.count(TagClassification.id)).scalar()
        active_classifications = db.query(func.count(TagClassification.id)).filter(
            TagClassification.is_active == True
        ).scalar()

        # 统计标签数量
        total_tags = db.query(func.count(Tag.id)).scalar()
        active_tags = db.query(func.count(Tag.id)).filter(Tag.is_active == True).scalar()

        # 按分类类型统计
        classification_type_stats = db.query(
            TagClassification.classification_type,
            func.count(TagClassification.id).label('count')
        ).group_by(TagClassification.classification_type).all()

        # 按域统计
        domain_stats = db.query(
            TagClassification.domain,
            func.count(TagClassification.id).label('count')
        ).filter(TagClassification.domain.isnot(None)).group_by(TagClassification.domain).all()

        return {
            "classifications": {
                "total": total_classifications,
                "active": active_classifications,
                "by_type": [{"type": stat[0], "count": stat[1]} for stat in classification_type_stats],
                "by_domain": [{"domain": stat[0], "count": stat[1]} for stat in domain_stats]
            },
            "tags": {
                "total": total_tags,
                "active": active_tags
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tag statistics: {str(e)}",
        )


@router.get(
    "/tags/popular",
    response_model=List[TagResponse],
    summary="获取热门标签",
    description="获取使用频率最高的标签 (需要标签读取权限)",
)
async def get_popular_tags_admin(
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    limit: int = Query(default=20, ge=1, le=100, description="返回数量限制"),
):
    """获取热门标签"""
    try:
        tags = tag_service.get_popular_tags(limit)
        return [TagResponse.model_validate(tag) for tag in tags]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular tags: {str(e)}",
        )


@router.get(
    "/tags/trending",
    response_model=List[TagResponse],
    summary="获取趋势标签",
    description="获取近期使用频率上升的标签 (需要标签读取权限)",
)
async def get_trending_tags_admin(
    current_user: Annotated[User, Depends(require_permission("tag.tag.read"))],
    tag_service: Annotated[TagService, Depends(get_tag_service)],
    limit: int = Query(default=20, ge=1, le=100, description="返回数量限制"),
):
    """获取趋势标签"""
    try:
        tags = tag_service.get_trending_tags(limit)
        return [TagResponse.model_validate(tag) for tag in tags]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get trending tags: {str(e)}",
        )
