"""
标签分类服务测试
"""

import pytest

from src.services.tag_classification_service.models import TagClassification
from src.services.tag_classification_service.schemas import (
    TagClassificationCreate,
    TagClassificationUpdate,
    ClassificationType,
)


class TestTagClassificationService:
    """标签分类服务测试类"""

    def test_create_classification_success(self, tag_classification_service):
        """测试成功创建标签分类"""
        # 准备测试数据
        classification_data = TagClassificationCreate(
            classification_code="finance.stock",
            classification_name="金融股票",
            classification_type=ClassificationType.CATEGORY,
            domain="finance",
            description="金融股票相关分类",
            icon="stock",
            color="#2196F3",
            sort_order=5,
        )

        # 执行创建操作
        classification = tag_classification_service.create_classification(classification_data)

        # 验证结果
        assert classification is not None
        assert classification.classification_code == "finance.stock"
        assert classification.classification_name == "金融股票"
        assert classification.classification_type == "category"
        assert classification.domain == "finance"
        assert classification.level == 1
        assert classification.path == "finance.stock"
        assert classification.is_active is True
        assert classification.is_system is False

    def test_create_classification_with_parent(self, tag_classification_service):
        """测试创建带父分类的标签分类"""
        # 先创建父分类
        parent_data = TagClassificationCreate(
            classification_code="finance",
            classification_name="金融",
            classification_type=ClassificationType.DOMAIN,
            description="金融领域",
        )
        parent = tag_classification_service.create_classification(parent_data)

        # 创建子分类
        child_data = TagClassificationCreate(
            classification_code="stock",
            classification_name="股票",
            classification_type=ClassificationType.CATEGORY,
            parent_id=parent.id,
            description="股票相关分类",
        )
        child = tag_classification_service.create_classification(child_data)

        # 验证结果
        assert child.parent_id == parent.id
        assert child.level == 2
        assert child.path == "finance.stock"

    def test_create_classification_duplicate_code(self, tag_classification_service):
        """测试创建重复代码的标签分类"""
        # 先创建一个分类
        classification_data = TagClassificationCreate(
            classification_code="test.duplicate",
            classification_name="测试重复",
            classification_type=ClassificationType.TYPE,
        )
        tag_classification_service.create_classification(classification_data)

        # 尝试创建相同代码的分类
        duplicate_data = TagClassificationCreate(
            classification_code="test.duplicate",
            classification_name="另一个测试",
            classification_type=ClassificationType.TYPE,
        )

        with pytest.raises(ValueError, match="already exists"):
            tag_classification_service.create_classification(duplicate_data)

    def test_get_classification_by_id(self, tag_classification_service):
        """测试根据ID获取标签分类"""
        # 创建分类
        classification_data = TagClassificationCreate(
            classification_code="test.get",
            classification_name="测试获取",
            classification_type=ClassificationType.TYPE,
        )
        created = tag_classification_service.create_classification(classification_data)

        # 获取分类
        retrieved = tag_classification_service.get_classification_by_id(created.id)

        # 验证结果
        assert retrieved is not None
        assert retrieved.id == created.id
        assert retrieved.classification_code == "test.get"

    def test_get_classification_by_code(self, tag_classification_service):
        """测试根据代码获取标签分类"""
        # 创建分类
        classification_data = TagClassificationCreate(
            classification_code="test.getcode",
            classification_name="测试获取代码",
            classification_type=ClassificationType.TYPE,
        )
        created = tag_classification_service.create_classification(classification_data)

        # 根据代码获取分类
        retrieved = tag_classification_service.get_classification_by_code("test.getcode")

        # 验证结果
        assert retrieved is not None
        assert retrieved.id == created.id
        assert retrieved.classification_code == "test.getcode"

    def test_get_classifications_with_filters(self, tag_classification_service):
        """测试带过滤条件获取分类列表"""
        # 创建不同类型的分类
        domain_data = TagClassificationCreate(
            classification_code="test.domain",
            classification_name="测试域",
            classification_type=ClassificationType.DOMAIN,
            domain="test",
        )
        domain = tag_classification_service.create_classification(domain_data)

        category_data = TagClassificationCreate(
            classification_code="test.category",
            classification_name="测试分类",
            classification_type=ClassificationType.CATEGORY,
            domain="test",
            parent_id=domain.id,
        )
        tag_classification_service.create_classification(category_data)

        # 按类型过滤
        domains = tag_classification_service.get_classifications(
            classification_type="domain"
        )
        assert len(domains) >= 1
        assert all(c.classification_type == "domain" for c in domains)

        # 按域过滤
        test_classifications = tag_classification_service.get_classifications(
            domain="test"
        )
        assert len(test_classifications) >= 2

    def test_update_classification(self, tag_classification_service):
        """测试更新标签分类"""
        # 创建分类
        classification_data = TagClassificationCreate(
            classification_code="test.update",
            classification_name="测试更新",
            classification_type=ClassificationType.TYPE,
        )
        created = tag_classification_service.create_classification(classification_data)

        # 更新分类
        update_data = TagClassificationUpdate(
            classification_name="更新后的名称",
            description="更新后的描述",
            color="#FF5722",
        )
        updated = tag_classification_service.update_classification(created.id, update_data)

        # 验证结果
        assert updated is not None
        assert updated.classification_name == "更新后的名称"
        assert updated.description == "更新后的描述"
        assert updated.color == "#FF5722"

    def test_delete_classification_success(self, tag_classification_service):
        """测试成功删除标签分类"""
        # 创建分类
        classification_data = TagClassificationCreate(
            classification_code="test.delete",
            classification_name="测试删除",
            classification_type=ClassificationType.TYPE,
        )
        created = tag_classification_service.create_classification(classification_data)

        # 删除分类
        result = tag_classification_service.delete_classification(created.id)

        # 验证结果
        assert result is True

        # 验证分类已被删除
        deleted = tag_classification_service.get_classification_by_id(created.id)
        assert deleted is None

    def test_delete_classification_with_children(self, tag_classification_service):
        """测试删除有子分类的分类"""
        # 创建父分类
        parent_data = TagClassificationCreate(
            classification_code="test.parent",
            classification_name="测试父分类",
            classification_type=ClassificationType.DOMAIN,
        )
        parent = tag_classification_service.create_classification(parent_data)

        # 创建子分类
        child_data = TagClassificationCreate(
            classification_code="test.child",
            classification_name="测试子分类",
            classification_type=ClassificationType.CATEGORY,
            parent_id=parent.id,
        )
        tag_classification_service.create_classification(child_data)

        # 尝试删除父分类
        with pytest.raises(ValueError, match="child classifications"):
            tag_classification_service.delete_classification(parent.id)

    def test_delete_nonexistent_classification(self, tag_classification_service):
        """测试删除不存在的分类"""
        result = tag_classification_service.delete_classification(99999)
        assert result is False
